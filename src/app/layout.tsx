import type { <PERSON>ada<PERSON> } from "next";
import { <PERSON>ei<PERSON>, <PERSON><PERSON><PERSON>_Mono } from "next/font/google";
import "./globals.css";
import { AppIntl } from "../i18n/app-intl";
import enMessages from "../i18n/locales/en.json";
import uaMessages from "../i18n/locales/uk.json";
import { RootProvider } from "@/context/root-context";

const geistSans = Geist({
  variable: "--font-geist-sans",
  subsets: ["latin"],
});

const geistMono = Geist_Mono({
  variable: "--font-geist-mono",
  subsets: ["latin"],
});

export const metadata: Metadata = {
  title: "Ivan - Professional Boxing Coach",
  description:
    "Professional boxing coach with 15+ years of experience. Offering personalized training programs from beginner fundamentals to advanced competitive preparation.",
};

export default function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {
  return (
    <html lang="en">
      <body
        className={`${geistSans.variable} ${geistMono.variable} antialiased`}
      >
        <RootProvider>
          <AppIntl enMessages={enMessages} uaMessages={uaMessages}>
            {children}
          </AppIntl>
        </RootProvider>
      </body>
    </html>
  );
}
