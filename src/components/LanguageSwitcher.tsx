"use client";

import { AppLocale, useRootContext } from "@/context/root-context";
import { Globe } from "lucide-react";

interface LanguageSwitcherProps {
  className?: string;
}

export const LanguageSwitcher = ({ className = "" }: LanguageSwitcherProps) => {
  const { locale, setLocale } = useRootContext();

  return (
    <button
      onClick={() => {
        setLocale(locale === AppLocale.en ? AppLocale.uk : AppLocale.en);
      }}
      className={`inline-flex items-center px-3 py-2 text-sm font-medium text-orange-100 hover:text-orange-200 transition-colors ${className}`}
      aria-label="Switch language"
    >
      <Globe className="w-4 h-4 mr-2" />
      <span className="uppercase font-semibold">
        {locale === "en" ? "EN" : "UA"}
      </span>
    </button>
  );
};
