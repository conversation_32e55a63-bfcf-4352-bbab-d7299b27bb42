"use client";

import React from "react";
import Image from "next/image";
import { useIntl } from "react-intl";
import {
  SparklesIcon,
  Trophy,
  GraduationCap,
  Award,
  Target,
} from "lucide-react";
import { Badge } from "@/components/ui/badge";
import appMessages from "@/app.messages";

function IvanPhoto() {
  return (
    <div className="relative flex items-center justify-center">
      <div className="relative rounded-3xl overflow-hidden shadow-2xl bg-gradient-to-br from-white/30 via-white/10 to-black/10 backdrop-blur-lg border border-white/20">
        <Image
          src="/assets/ivan.webp"
          alt="Ivan - Professional Boxing Coach"
          width={340}
          height={420}
          className="object-cover w-[340px] h-[420px] rounded-3xl"
        />
        <div className="absolute inset-0 pointer-events-none rounded-3xl bg-gradient-to-t from-black/40 via-transparent to-transparent" />
      </div>
    </div>
  );
}

function IvanDescription({
  items,
  align,
}: {
  items: { icon: React.ReactNode; label: string; desc: string }[];
  align?: "left" | "right";
}) {
  return (
    <div
      className={`flex flex-col gap-6 p-6 rounded-3xl shadow-xl bg-gradient-to-br ${
        align === "left"
          ? "from-[#f8fafc] via-[#e0e7ff] to-[#f0fdfa] dark:from-[#18181b] dark:via-[#312e81] dark:to-[#0f172a]"
          : "from-[#f8fafc] via-[#fbcfe8] to-[#f0fdfa] dark:from-[#18181b] dark:via-[#831843] dark:to-[#0f172a]"
      } border border-white/10 min-w-[260px] max-w-[320px]`}
    >
      <ul className="flex flex-col gap-4">
        {items.map((item, idx) => (
          <li key={idx} className="flex items-start gap-3">
            <span className="flex-shrink-0 rounded-lg bg-white/70 dark:bg-black/40 p-2 shadow">
              {item.icon}
            </span>
            <div>
              <span className="font-semibold text-neutral-800 dark:text-neutral-200">
                {item.label}
              </span>
              <div className="text-sm text-neutral-600 dark:text-neutral-400">
                {item.desc}
              </div>
            </div>
          </li>
        ))}
      </ul>
    </div>
  );
}

export function AboutIvan() {
  const { formatMessage: t } = useIntl();
  return (
    <section
      id="about"
      className="w-full py-16 md:py-24 bg-gradient-to-br from-white via-[#f8fafc] to-[#e0e7ff] dark:from-black dark:via-[#18181b] dark:to-[#312e81]"
    >
      <div className="text-center mb-14 md:mb-20 px-4 md:px-6">
        <Badge
          variant="outline"
          className="mb-6 md:mb-8 rounded-[14px] border border-black/10 bg-white text-sm md:text-base dark:border-white/5 dark:bg-neutral-800/5"
        >
          <SparklesIcon className="fill-[#EEBDE0] stroke-1 text-neutral-800 mr-2" />
          {t(appMessages.aboutAchievements)}
        </Badge>
        <h2 className="text-4xl md:text-6xl font-extrabold text-neutral-900 dark:text-white mb-4 tracking-tight">
          {t(appMessages.aboutTitle)}
        </h2>
        <h3 className="text-2xl md:text-3xl font-bold text-orange-600 mb-2">
          {t(appMessages.aboutCoachName)}
        </h3>
        <p className="text-lg md:text-2xl text-neutral-700 dark:text-neutral-400 max-w-3xl mx-auto">
          {t(appMessages.aboutDescription)}
        </p>
      </div>
      <div className="flex flex-col md:flex-row items-center justify-center gap-8 md:gap-12 max-w-6xl mx-auto px-4 md:px-0">
        <IvanDescription
          align="left"
          items={[
            {
              icon: (
                <GraduationCap className="h-5 w-5 text-indigo-700 dark:text-indigo-300" />
              ),
              label: t(appMessages.aboutEducation),
              desc: t(appMessages.aboutEducationDesc),
            },
            {
              icon: (
                <Trophy className="h-5 w-5 text-yellow-600 dark:text-yellow-300" />
              ),
              label: t(appMessages.aboutMasterSport),
              desc: t(appMessages.aboutMasterSportDesc),
            },
          ]}
        />
        <IvanPhoto />
        <IvanDescription
          align="right"
          items={[
            {
              icon: (
                <Award className="h-5 w-5 text-pink-600 dark:text-pink-300" />
              ),
              label: t(appMessages.aboutChampion),
              desc: t(appMessages.aboutChampionDesc),
            },
            {
              icon: (
                <Target className="h-5 w-5 text-green-600 dark:text-green-300" />
              ),
              label: t(appMessages.aboutSpecialization),
              desc: t(appMessages.aboutSpecializationDesc),
            },
          ]}
        />
      </div>
    </section>
  );
}
